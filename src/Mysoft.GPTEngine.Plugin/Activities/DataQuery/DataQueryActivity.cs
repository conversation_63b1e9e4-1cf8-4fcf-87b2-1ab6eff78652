using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Microsoft.SemanticKernel.Connectors.OpenAI;
using Mysoft.GPTEngine.Common.CustomerException;
using Mysoft.GPTEngine.Domain;
using Mysoft.GPTEngine.Domain.AgentSkillEnhancement;
using Mysoft.GPTEngine.Domain.Entity;
using Mysoft.GPTEngine.Domain.Extensions;
using Mysoft.GPTEngine.Domain.Repositories;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Enums;
using Mysoft.GPTEngine.Domain.Shared.Event;
using Mysoft.GPTEngine.Domain.Services;
using Mysoft.GPTEngine.Plugin.System;
using Mysoft.GPTEngine.Plugin.Resources;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using static Mysoft.GPTEngine.Domain.Shared.Dtos.FlowNode;

namespace Mysoft.GPTEngine.Plugin.Activities.DataQuery
{
    public class DataQueryActivity : SemanticKernelActivity
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly AgentSkillEventHelper _eventHelper;
        private readonly ConversationMemoryService _conversationMemoryService;
        private readonly KnowledgeRepository _knowledgeRepository;
        private AgentServiceHelper? _agentServiceHelper;
        private string? _input;
        private CancellationToken _cancellationToken;

        // 思考模式配置（硬编码测试）
        private ThinkingModeOptions? ThinkingModeOptions { get; set; }

        public DataQueryActivity(IServiceProvider serviceProvider, ILogger<DataQueryActivity> logger) : base(serviceProvider, logger)
        {
            _serviceProvider = serviceProvider;
            _eventHelper = new AgentSkillEventHelper(_httpContextAccessor);
            _conversationMemoryService = serviceProvider.GetRequiredService<ConversationMemoryService>();
            _knowledgeRepository = serviceProvider.GetRequiredService<KnowledgeRepository>();

            // 硬编码思考模式配置用于测试
            ThinkingModeOptions = new ThinkingModeOptions
            {
                EnableThinking = false // 可以根据需要调整
            };
        }

        [KernelFunction(nameof(DataQueryActivity))]
        [Description("数据查询智能体")]
        public new Task ExecuteAsync(CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("[DataQueryActivity] KernelFunction ExecuteAsync 被调用");
            _cancellationToken = cancellationToken;
            return base.ExecuteAsync(cancellationToken);
        }

        [KernelFunction("DataQueryAndSummary")]
        [Description("数据查询与总结")]
        public Task DataQueryAndSummaryAsync(CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("[DataQueryActivity] KernelFunction DataQueryAndSummaryAsync 被调用");
            _cancellationToken = cancellationToken;
            return base.ExecuteAsync(cancellationToken);
        }

        protected override async Task<FlowNode?> PreExecuteActivityAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("[DataQueryActivity] PreExecuteActivityAsync 开始");
            var flowNode = await base.PreExecuteActivityAsync(cancellationToken);
            if (flowNode == null)
            {
                _logger.LogWarning("[DataQueryActivity] PreExecuteActivityAsync flowNode 为空");
                return flowNode;
            }

            _logger.LogInformation("[DataQueryActivity] PreExecuteActivityAsync 完成，flowNode: {nodeCode}", flowNode.Code);
            return flowNode;
        }

        protected override async Task ExecuteActivityAsync(FlowNode flowNode, CancellationToken cancellationToken)
        {
            _logger.LogInformation("[DataQueryActivity] ExecuteActivityAsync 开始");
            await DataQueryDelegate(flowNode, cancellationToken);
            _logger.LogInformation("[DataQueryActivity] ExecuteActivityAsync 完成");
        }

        private async Task<string> DataQueryDelegate(FlowNode flowNode, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("[DataQueryActivity] DataQueryDelegate 开始");

                // 解析输入参数
                await _chatRunDto.InputsArgumentParser(flowNode);
                var inputs = flowNode.Config.Inputs;
                _input = inputs.FirstOrDefault(x => x.Code == "input")?.LiteralValue ?? _input;

                _logger.LogInformation("[DataQueryActivity] 解析到的输入参数: {input}", _input);
                _logger.LogInformation("[DataQueryActivity] 输入配置详情: {inputs}",
                    string.Join(", ", inputs.Select(i => $"{i.Code}={i.LiteralValue} (ref:{i.Value?.Content})")));

                // 如果从配置中没有获取到输入，尝试从ChatArguments中获取
                if (string.IsNullOrEmpty(_input))
                {
                    _input = await _chatRunDto.GetArgumentValue<string>("NodeOutput_start0_input");
                    _logger.LogInformation("[DataQueryActivity] 从ChatArguments获取到的输入: {input}", _input);
                }

                // 如果还是没有，尝试获取系统输入
                if (string.IsNullOrEmpty(_input))
                {
                    _input = await _chatRunDto.GetArgumentValue<string>("System_Input");
                    _logger.LogInformation("[DataQueryActivity] 从System_Input获取到的输入: {input}", _input);
                }

                if (string.IsNullOrEmpty(_input))
                {
                    _logger.LogWarning("[DataQueryActivity] 用户输入为空");
                    var testResponse = "DataQueryActivity测试：用户输入为空，但节点已成功执行。";
                    await AddFirstOutput(flowNode, testResponse);
                    return testResponse;
                }

                _logger.LogInformation("[DataQueryActivity] 开始处理用户输入: {input}", _input);

                // 创建工具标题映射Dictionary，确保线程安全
                var toolTitleMapping = new ConcurrentDictionary<string, string>();

                // 将映射存储到HttpContext中，供PluginFilter使用 - 关键修复：确保PluginFilter能获取到工具标题
                _httpContextAccessor.HttpContext.Items["ToolTitleMapping"] = toolTitleMapping;

                // 创建Kernel并添加知识库工具（支持TypeEnum=1和TypeEnum=3）
                Kernel kernel = await CreateKernelWithKnowledgeTools(toolTitleMapping);

                // 获取聊天完成服务
                var chatCompletionService = kernel.GetRequiredService<IChatCompletionService>();

                _logger.LogInformation("[DataQueryActivity] 使用的模型实例: {modelCode}", _chatRunDto.ModelInstance?.ModelCode);
                _logger.LogInformation("[DataQueryActivity] 聊天完成服务类型: {serviceType}", chatCompletionService.GetType().Name);

                // 创建ChatHistory并加载历史消息
                var chat = await CreateChatHistoryWithMemory(_input);

                _logger.LogInformation("[DataQueryActivity] ChatHistory创建完成，消息数: {count}", chat.Count);

                _logger.LogInformation("[DataQueryActivity] 开始流式调用大模型");

                // 保存ChatHistory到ChatRunDto，并记录初始长度（用于后续提取工具调用消息）
                _chatRunDto.ChatHistory = chat;
                _chatRunDto.InitialChatHistoryCount = chat.Count;
                _logger.LogInformation("[DataQueryActivity] 记录初始ChatHistory长度: {count}", _chatRunDto.InitialChatHistoryCount);

                // 执行流式聊天完成
                string chatOutput = "";
                var executionSettings = CreateExecutionSettingsForDataQuery();

                IAsyncEnumerable<StreamingChatMessageContent> response = chatCompletionService.GetStreamingChatMessageContentsAsync(
                    chat, executionSettings, kernel, cancellationToken);

                await foreach (StreamingChatMessageContent content in response)
                {
                    // 处理思考模式下的思考输出
                    if (ThinkingModeOptions?.EnableThinking == true)
                    {
                        var agentServiceHelper = GetAgentServiceHelper();
                        var thinkingContent = agentServiceHelper.ExtractThinkingContent(content, _eventHelper, _cancellationToken);
                        if (!string.IsNullOrEmpty(thinkingContent))
                        {
                            _logger.LogInformation("[DataQueryActivity] 思考内容: {thinkingContent}", thinkingContent);
                        }
                    }

                    if (content?.Content != null)
                    {
                        string responseContent = content.Content;
                        WriteDataQueryChatMessage(content);
                        chatOutput += responseContent;
                    }
                }

                _logger.LogInformation("[DataQueryActivity] 数据查询完成，输出: {output}", chatOutput);

                // 保存会话记忆（包含工具调用）
                await SaveConversationMemory(_input, chatOutput);

                // 设置输出参数
                await AddFirstOutput(flowNode, chatOutput);

                // 推送字段事件
                flowNode.Config.Outputs.ForEach(async f =>
                {
                    FieldEvent fieldEvent = new FieldEvent(flowNode.Code, string.Format(KernelArgumentsConstant.NodeOutput, flowNode.Code, f.Code), f.LiteralValue);
                    await _chatRunDto.EventBus.PublishAsync(fieldEvent);
                });

                await base.PostExecuteActivityAsync(flowNode, cancellationToken);
                return chatOutput;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[DataQueryActivity] 执行数据查询时发生异常: {message}", ex.Message);
                throw;
            }
        }

        /// <summary>
        /// 创建包含知识库工具的Kernel（支持TypeEnum=1和TypeEnum=3）
        /// </summary>
        private async Task<Kernel> CreateKernelWithKnowledgeTools(ConcurrentDictionary<string, string> toolTitleMapping)
        {
            var builder = Kernel.CreateBuilder();

            // 添加PluginFilter - 关键修复：确保工具调用事件能够正确发送到界面
            builder.Services.AddSingleton<IFunctionInvocationFilter>(sp =>
            {
                var loggerFactory = sp.GetService<ILoggerFactory>();
                var pluginLogger = loggerFactory?.CreateLogger<PluginFilter>();
                return new PluginFilter(_httpContextAccessor, pluginLogger);
            });

            // 添加模型服务，使用与AgentSkillDomainService相同的配置方式
            var modelInstance = _chatRunDto.ModelInstance;
            if (modelInstance == null)
            {
                throw new ArgumentNullException("ModelInstance不能为空");
            }

            _logger.LogInformation("[CreateKernelWithKnowledgeTools] 使用模型实例: {modelCode}, CustomModelCode: {customModelCode}",
                modelInstance.ModelCode, modelInstance.CustomModelCode);

            // 调试API Key格式
            var apiKeyPrefix = modelInstance.ApiKey?.Length > 10 ? modelInstance.ApiKey.Substring(0, 10) : modelInstance.ApiKey;
            _logger.LogInformation("[CreateKernelWithKnowledgeTools] API Key前缀: {apiKeyPrefix}..., 长度: {length}",
                apiKeyPrefix, modelInstance.ApiKey?.Length ?? 0);

            List<ModelInstanceDto> textModelInstances = new List<ModelInstanceDto>();
            textModelInstances.Add(modelInstance);

            // 使用useAgent=true，与AgentSkillDomainService保持一致，并传递思考模式配置
            await builder.AddChatCompletionServices(textModelInstances, useAgent: true, ThinkingModeOptions);

            var kernel = builder.Build();
            _logger.LogInformation("[CreateKernelWithKnowledgeTools] Kernel构建完成，开始添加知识库工具");

            // 添加TypeEnum=1的知识库工具
            await AddKnowledgePlugin(kernel, toolTitleMapping);

            // 添加TypeEnum=3的知识库工具
            await AddDataKnowledgePlugin(kernel, toolTitleMapping);

            _logger.LogInformation("[CreateKernelWithKnowledgeTools] Kernel已注册插件列表: {plugins}",
                string.Join(", ", kernel.Plugins.Select(p => p.Name)));

            return kernel;
        }

        /// <summary>
        /// 添加TypeEnum=3的知识库插件
        /// </summary>
        private async Task AddDataKnowledgePlugin(Kernel kernel, ConcurrentDictionary<string, string> toolTitleMapping)
        {
            try
            {
                // 获取当前流程节点
                var currentFlowNode = await _chatRunDto.GetFlowNode();
                if (currentFlowNode == null)
                {
                    _logger.LogWarning("[AddDataKnowledgePlugin] 无法获取当前流程节点");
                    return;
                }

                // 获取知识库配置
                var knowledges = currentFlowNode.Config.Knowledges;
                if (knowledges == null || !knowledges.Any())
                {
                    _logger.LogInformation("[AddDataKnowledgePlugin] 没有配置知识库");
                    return;
                }

                _logger.LogInformation("[AddDataKnowledgePlugin] 开始处理 {count} 个知识库", knowledges.Count);

                // 使用DataKnowledgeImporter处理TypeEnum=3的知识库
                var dataKnowledgeImporter = new DataKnowledgeImporter(kernel, _serviceProvider);
                await dataKnowledgeImporter.QueryAllVectorDatabaseDataAsync(knowledges.ToArray(), toolTitleMapping);

                _logger.LogInformation("[AddDataKnowledgePlugin] TypeEnum=3知识库处理完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[AddDataKnowledgePlugin] 添加数据知识库插件时发生异常: {message}", ex.Message);
            }
        }

        /// <summary>
        /// 写入流式聊天消息
        /// </summary>
        private void WriteDataQueryChatMessage(StreamingChatMessageContent response)
        {
            if (response == null || response.Content == null) return;
            _logger.LogDebug("[DataQueryActivity] 流式输出: {content}", response.Content);
            _eventHelper.TextEvent(response.Content, _cancellationToken).GetAwaiter().GetResult();
        }

        protected override async Task PostExecuteActivityAsync(FlowNode flowNode, CancellationToken cancellationToken)
        {
            await base.PostExecuteActivityAsync(flowNode, cancellationToken);
        }

        /// <summary>
        /// 获取或创建AgentServiceHelper实例
        /// </summary>
        private AgentServiceHelper GetAgentServiceHelper()
        {
            if (_agentServiceHelper == null)
            {
                var knowledgeDomainService = _serviceProvider.GetRequiredService<IKnowledgeDomainService>();
                var mysoftApiDomainService = _serviceProvider.GetRequiredService<MysoftApiService>();
                var agentServiceHelperLogger = _serviceProvider.GetRequiredService<ILogger<AgentServiceHelper>>();

                _agentServiceHelper = new AgentServiceHelper(
                    knowledgeDomainService,
                    _kernel,
                    mysoftApiDomainService,
                    _mysoftContextFactory,
                    _httpContextAccessor,
                    _mapper,
                    agentServiceHelperLogger,
                    _knowledgeRepository);
            }
            return _agentServiceHelper;
        }

        /// <summary>
        /// 添加TypeEnum=1的知识库插件 - 复用AgentSkillDomainService的逻辑
        /// </summary>
        private async Task AddKnowledgePlugin(Kernel kernel, ConcurrentDictionary<string, string> toolTitleMapping)
        {
            await AddKnowledgePlugin(kernel, toolTitleMapping, 0.1);
        }

        private async Task AddKnowledgePlugin(Kernel kernel, ConcurrentDictionary<string, string> toolTitleMapping, double minRelevanceScore)
        {
            try
            {
                // 获取当前流程节点
                var currentFlowNode = await _chatRunDto.GetFlowNode();
                if (currentFlowNode == null)
                {
                    _logger.LogWarning("[AddKnowledgePlugin] 无法获取当前流程节点");
                    return;
                }

                // 获取知识库配置
                var knowledges = currentFlowNode.Config.Knowledges;
                if (knowledges == null || !knowledges.Any())
                {
                    _logger.LogInformation("[AddKnowledgePlugin] 没有配置知识库");
                    return;
                }

                _logger.LogInformation("[AddKnowledgePlugin] 开始处理 {count} 个知识库", knowledges.Count);

                // 批量查询所有知识库的TypeEnum信息
                var agentServiceHelper = GetAgentServiceHelper();
                var knowledgeTypeMap = await agentServiceHelper.GetKnowledgeTypeMapAsync(knowledges.ToArray());

                // 过滤出TypeEnum为1的知识库，排除TypeEnum为3的知识库
                var filteredKnowledges = new List<AgentKnowledgeDto>();

                foreach (var knowledgeCode in knowledges)
                {
                    try
                    {
                        if (!knowledgeTypeMap.TryGetValue(knowledgeCode, out var typeEnum))
                        {
                            _logger.LogWarning("[AddKnowledgePlugin] 未找到知识库实体: {code}", knowledgeCode);
                            continue;
                        }

                        if (typeEnum == 1)
                        {
                            // 创建AgentKnowledgeDto对象
                            var knowledgeEntity = await _knowledgeRepository.GetFirstAsync(k => k.Code == knowledgeCode);

                            if (knowledgeEntity != null)
                            {
                                var agentKnowledge = new AgentKnowledgeDto
                                {
                                    Id = knowledgeEntity.KnowledgeGUID.ToString(),
                                    Code = knowledgeEntity.Code,
                                    Name = knowledgeEntity.Name
                                };
                                filteredKnowledges.Add(agentKnowledge);
                                _logger.LogInformation("[AddKnowledgePlugin] 添加TypeEnum=1的知识库到KnowledgeToolsImporter: {code}", knowledgeCode);
                            }
                        }
                        else if (typeEnum == 3)
                        {
                            _logger.LogInformation("[AddKnowledgePlugin] 跳过TypeEnum=3的知识库，将由专门逻辑处理: {code}", knowledgeCode);
                        }
                        else
                        {
                            _logger.LogInformation("[AddKnowledgePlugin] 跳过TypeEnum={typeEnum}的知识库: {code}", typeEnum, knowledgeCode);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "[AddKnowledgePlugin] 处理知识库 {code} 时发生异常: {message}", knowledgeCode, ex.Message);
                    }
                }

                if (filteredKnowledges.Any())
                {
                    _logger.LogInformation("[AddKnowledgePlugin] 向KnowledgeToolsImporter传递 {count} 个TypeEnum=1的知识库", filteredKnowledges.Count);
                    await new KnowledgeToolsImporter(kernel, _serviceProvider, minRelevanceScore).ImportKnowledgeTools(filteredKnowledges, toolTitleMapping);
                }
                else
                {
                    _logger.LogInformation("[AddKnowledgePlugin] 没有TypeEnum=1的知识库需要处理");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[AddKnowledgePlugin] 添加TypeEnum=1知识库插件时发生异常: {message}", ex.Message);
            }
        }



        /// <summary>
        /// 创建包含历史消息的ChatHistory - 复用AgentSkillDomainService的逻辑
        /// </summary>
        private async Task<ChatHistory> CreateChatHistoryWithMemory(string userInput)
        {
            try
            {
                // 创建一个全新的ChatHistory
                ChatHistory chat = new ChatHistory();
                _logger.LogInformation("[CreateChatHistoryWithMemory] 创建新的ChatHistory，初始消息数: {count}", chat.Count);

                // 获取用户GUID和会话GUID作为会话标识符
                string userGuid = _chatRunDto.Chat?.UserGUID ?? string.Empty;
                string chatGuid = _chatRunDto.Chat?.ChatGUID.ToString() ?? string.Empty;
                _logger.LogInformation("[CreateChatHistoryWithMemory] 用户GUID: {userGuid}, 会话GUID: {chatGuid}", userGuid, chatGuid);

                // 添加系统提示 - 从资源文件读取并进行变量替换
                var systemPromptTemplate = EmbeddedResource.Read(EmbeddedResource.DataQuery);
                _logger.LogInformation("[CreateChatHistoryWithMemory] 从资源文件读取的模板: {template}", systemPromptTemplate);

                var arguments = new KernelArguments()
                {
                    ["now"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };
                _logger.LogInformation("[CreateChatHistoryWithMemory] 模板参数: {@arguments}", arguments);

                var agentServiceHelper = GetAgentServiceHelper();
                var templateResult = await agentServiceHelper.TemplateRenderAsync(systemPromptTemplate, arguments);
                var systemPrompt = templateResult.Content;
                _logger.LogInformation("[CreateChatHistoryWithMemory] 模板渲染后的系统提示: {systemPrompt}", systemPrompt);

                chat.AddMessage(AuthorRole.System, systemPrompt);
                _logger.LogInformation("[CreateChatHistoryWithMemory] 已添加系统提示，当前ChatHistory消息数: {count}", chat.Count);

                // 如果有用户GUID和会话GUID，加载历史会话记忆（只加载用户-助手对话，不包含系统提示）
                if (!string.IsNullOrEmpty(userGuid) && !string.IsNullOrEmpty(chatGuid))
                {
                    // 使用默认的maxTurn配置，可以根据需要调整
                    int maxTurn = 10; // 硬编码测试值，后续可以从配置获取
                    chat = await _conversationMemoryService.LoadConversationMemoryAsync(chat, userGuid, chatGuid, maxTurn);
                    _logger.LogInformation("[CreateChatHistoryWithMemory] 已为用户 {userGuid} 会话 {chatGuid} 加载会话记忆，使用maxTurn: {maxTurn}，当前ChatHistory消息数: {count}", userGuid, chatGuid, maxTurn, chat.Count);
                }

                // 验证和修复ChatHistory中的工具调用序列 - 复用AgentServiceHelper的方法
                chat = chat.ValidateAndFixToolCalls(_logger);
                _logger.LogInformation("[CreateChatHistoryWithMemory] 验证和修复工具调用序列后，ChatHistory消息数: {count}", chat.Count);

                // 最后添加当前用户消息
                chat.AddMessage(AuthorRole.User, userInput);
                _logger.LogInformation("[CreateChatHistoryWithMemory] 添加用户消息后，ChatHistory消息数: {count}", chat.Count);

                return chat;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[CreateChatHistoryWithMemory] 创建ChatHistory时发生错误");
                // 如果出错，返回基本的ChatHistory
                var fallbackChat = new ChatHistory();
                try
                {
                    // 尝试使用资源文件，如果失败则使用硬编码
                    var fallbackSystemPromptTemplate = EmbeddedResource.Read(EmbeddedResource.DataQuery);
                    var fallbackArguments = new KernelArguments()
                    {
                        ["now"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                    };
                    var fallbackAgentServiceHelper = GetAgentServiceHelper();
                    var fallbackTemplateResult = await fallbackAgentServiceHelper.TemplateRenderAsync(fallbackSystemPromptTemplate, fallbackArguments);
                    fallbackChat.AddMessage(AuthorRole.System, fallbackTemplateResult.Content);
                }
                catch
                {
                    // 如果资源文件读取失败，使用硬编码的提示
                    fallbackChat.AddMessage(AuthorRole.System, "你是一个数据查询智能助手，能够帮助用户查询和分析数据。请根据用户的需求，使用可用的工具来获取相关数据并提供准确的回答。");
                }
                fallbackChat.AddMessage(AuthorRole.User, userInput);
                return fallbackChat;
            }
        }

        /// <summary>
        /// 创建数据查询专用的ExecutionSettings，包含工具调用配置
        /// </summary>
        /// <returns>配置好的OpenAIPromptExecutionSettings</returns>
        private OpenAIPromptExecutionSettings CreateExecutionSettingsForDataQuery()
        {
            var executionSettings = new OpenAIPromptExecutionSettings
            {
                // 关键：启用自动工具调用行为，这是工具调用的核心配置
                FunctionChoiceBehavior = FunctionChoiceBehavior.Auto()
            };

            _logger.LogInformation("[CreateExecutionSettingsForDataQuery] 已创建包含工具调用配置的ExecutionSettings");
            return executionSettings;
        }

        /// <summary>
        /// 保存会话记忆 - 只保存工具调用和助手回复，避免重复保存用户消息
        /// </summary>
        private async Task SaveConversationMemory(string userInput, string assistantOutput)
        {
            try
            {
                // 获取用户GUID和会话GUID作为会话标识符
                string userGuid = _chatRunDto.Chat?.UserGUID ?? string.Empty;
                string chatGuid = _chatRunDto.Chat?.ChatGUID.ToString() ?? string.Empty;

                if (!string.IsNullOrEmpty(userGuid) && !string.IsNullOrEmpty(chatGuid))
                {
                    var newMessages = new List<ChatMessageDto>();
                    var batchGuid = _chatRunDto?.BatchGuid ?? Guid.NewGuid();
                    var userName = _chatRunDto?.Chat?.UserName ?? string.Empty;

                    // 只提取工具调用相关的消息，不包含用户消息（避免重复）
                    var agentServiceHelper = GetAgentServiceHelper();

                    // 检查ChatHistory中是否有新增的工具调用消息
                    var chatHistory = _chatRunDto.ChatHistory;
                    if (chatHistory != null && _chatRunDto.InitialChatHistoryCount.HasValue)
                    {
                        var initialCount = _chatRunDto.InitialChatHistoryCount.Value;
                        var currentCount = chatHistory.Count;

                        _logger.LogInformation("[SaveConversationMemory] ChatHistory初始长度: {initial}, 当前长度: {current}", initialCount, currentCount);

                        // 提取新增的工具调用消息（跳过用户消息和最后一条助手回复）
                        for (int i = initialCount; i < currentCount - 1; i++)
                        {
                            var message = chatHistory[i];
                            // 只处理工具调用和工具返回消息，跳过用户消息
                            if (message.Role == AuthorRole.Assistant || message.Role == AuthorRole.Tool)
                            {
                                var messageDto = agentServiceHelper.ConvertChatMessageToDto(message, newMessages.Count);
                                if (messageDto != null)
                                {
                                    messageDto.BatchGUID = batchGuid;
                                    messageDto.UserName = userName;
                                    messageDto.Index = newMessages.Count;
                                    newMessages.Add(messageDto);
                                }
                            }
                        }
                    }

                    // 添加最终的助手回复
                    newMessages.Add(new ChatMessageDto
                    {
                        Role = ChatRoleConstant.Assistant,
                        Content = assistantOutput,
                        Index = newMessages.Count,
                        BatchGUID = batchGuid,
                        UserName = userName,
                        IsHidden = 0
                    });

                    if (newMessages.Any())
                    {
                        // 获取租户信息
                        var tenantCode = _chatRunDto?.Chat?.TenantCode ?? string.Empty;
                        var tenantName = _chatRunDto?.Chat?.TenantName ?? string.Empty;

                        // 保存会话记忆到缓存和数据库 - 复用ConversationMemoryService的方法
                        await _conversationMemoryService.SaveConversationMessagesAsync(
                            userGuid, chatGuid, newMessages, tenantCode, tenantName, batchGuid, userName);

                        _logger.LogInformation("[SaveConversationMemory] 已保存 {count} 条消息到会话记忆（不包含用户消息），用户: {userGuid}, 会话: {chatGuid}",
                            newMessages.Count, userGuid, chatGuid);
                    }
                }
                else
                {
                    _logger.LogWarning("[SaveConversationMemory] 用户GUID或会话GUID为空，跳过会话记忆保存");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[SaveConversationMemory] 保存会话记忆时发生错误");
            }
        }
    }
}
